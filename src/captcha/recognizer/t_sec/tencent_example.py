"""腾讯滑块验证码使用示例。

展示如何使用腾讯滑块验证码解决器。
"""

import asyncio
import logging

from common.utils import logging_utils
from .tencent_slider_solver import TencentSliderSolver

logger = logging.getLogger(__name__)


async def example_1_basic_usage():
    """示例1: 基本使用方法。"""
    print("=== 示例1: 基本使用 ===")
    
    # 创建腾讯滑块解决器
    solver = TencentSliderSolver(
        dddocr_base_url="http://10.168.1.201:7777",
        headless=False,  # 显示浏览器以便观察
        browser_type="chromium"
    )
    
    # 使用异步上下文管理器
    async with solver:
        # 导航到包含腾讯滑块验证码的页面
        test_url = "https://007.qq.com/online.html"  # 腾讯验证码演示页面
        await solver.navigate_to_page(test_url)
        
        # 等待页面加载
        await asyncio.sleep(3)
        
        # 尝试触发验证码（可能需要点击某个按钮）
        try:
            # 查找并点击可能触发验证码的元素
            trigger_button = await solver.page.query_selector('button, .btn, input[type="submit"]')
            if trigger_button:
                await trigger_button.click()
                print("已触发验证码")
        except:
            print("未找到触发按钮，验证码可能已经显示")
        
        # 解决滑块验证码
        success = await solver.solve_captcha(max_retries=3)
        
        if success:
            print("✅ 验证码解决成功！")
        else:
            print("❌ 验证码解决失败")
        
        # 等待观察结果
        await asyncio.sleep(5)


async def example_2_login_scenario():
    """示例2: 登录场景中的验证码处理。"""
    print("\n=== 示例2: 登录场景 ===")
    
    solver = TencentSliderSolver(
        dddocr_base_url="http://10.168.1.201:7777",
        headless=False
    )
    
    async with solver:
        # 模拟登录流程
        login_url = "https://example.com/login"  # 替换为实际的登录页面
        
        try:
            await solver.navigate_to_page(login_url)
            
            # 填写登录信息
            await solver.page.fill('#username', 'your_username')
            await solver.page.fill('#password', 'your_password')
            
            # 点击登录按钮
            await solver.page.click('#login-button')
            
            # 等待验证码出现
            captcha_appeared = await solver.wait_for_captcha(timeout=10000)
            
            if captcha_appeared:
                print("检测到腾讯滑块验证码")
                
                # 解决验证码
                success = await solver.solve_captcha(max_retries=5)
                
                if success:
                    print("验证码解决成功，继续登录流程")
                    
                    # 等待登录结果
                    await asyncio.sleep(3)
                    
                    # 检查是否登录成功
                    current_url = solver.page.url
                    if 'dashboard' in current_url or 'home' in current_url:
                        print("✅ 登录成功！")
                    else:
                        print("登录可能失败，请检查")
                else:
                    print("❌ 验证码解决失败，登录中断")
            else:
                print("未检测到验证码，可能直接登录成功")
        
        except Exception as e:
            print(f"登录过程出错: {e}")


async def example_3_batch_testing():
    """示例3: 批量测试多个页面。"""
    print("\n=== 示例3: 批量测试 ===")
    
    # 测试页面列表
    test_pages = [
        "https://007.qq.com/online.html",
        "https://cloud.tencent.com/product/captcha",
        # 添加更多测试页面
    ]
    
    solver = TencentSliderSolver(
        dddocr_base_url="http://10.168.1.201:7777",
        headless=True  # 批量测试时使用无头模式
    )
    
    results = []
    
    async with solver:
        for i, url in enumerate(test_pages, 1):
            print(f"\n测试页面 {i}/{len(test_pages)}: {url}")
            
            try:
                await solver.navigate_to_page(url)
                await asyncio.sleep(2)
                
                # 尝试解决验证码
                success = await solver.solve_captcha(max_retries=2)
                
                results.append({
                    'url': url,
                    'success': success,
                    'error': None
                })
                
                print(f"结果: {'成功' if success else '失败'}")
                
            except Exception as e:
                results.append({
                    'url': url,
                    'success': False,
                    'error': str(e)
                })
                print(f"出错: {e}")
    
    # 输出测试总结
    print("\n=== 测试总结 ===")
    success_count = sum(1 for r in results if r['success'])
    print(f"总测试页面: {len(results)}")
    print(f"成功解决: {success_count}")
    print(f"成功率: {success_count/len(results)*100:.1f}%")
    
    for result in results:
        status = "✅" if result['success'] else "❌"
        print(f"{status} {result['url']}")
        if result['error']:
            print(f"   错误: {result['error']}")


async def example_4_custom_configuration():
    """示例4: 自定义配置。"""
    print("\n=== 示例4: 自定义配置 ===")
    
    # 自定义配置
    solver = TencentSliderSolver(
        dddocr_base_url="http://10.168.1.201:7777",
        headless=False,
        browser_type="chromium"  # 可以选择 "chromium", "firefox", "webkit"
    )
    
    async with solver:
        # 设置自定义的浏览器选项
        await solver.page.set_viewport_size({"width": 1366, "height": 768})
        
        # 设置自定义的用户代理
        await solver.page.set_extra_http_headers({
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"
        })
        
        # 导航到测试页面
        test_url = input("请输入测试页面URL: ").strip()
        if not test_url:
            print("未输入URL，跳过测试")
            return
        
        await solver.navigate_to_page(test_url)
        
        # 等待用户手动操作
        print("请在浏览器中进行必要的操作以触发验证码...")
        input("操作完成后按回车继续...")
        
        # 解决验证码
        success = await solver.solve_captcha(max_retries=3)
        
        print(f"验证码解决结果: {'成功' if success else '失败'}")
        
        # 保持浏览器打开以便观察
        print("浏览器将保持打开10秒...")
        await asyncio.sleep(10)


async def main():
    """主函数。"""
    print("腾讯滑块验证码使用示例")
    print("=" * 50)
    
    # 配置日志
    logging.basicConfig(level=logging.INFO)
    
    try:
        print("选择要运行的示例:")
        print("1. 基本使用")
        print("2. 登录场景")
        print("3. 批量测试")
        print("4. 自定义配置")
        print("5. 运行所有示例")
        
        choice = input("请选择 (1-5): ").strip()
        
        if choice == "1":
            await example_1_basic_usage()
        elif choice == "2":
            await example_2_login_scenario()
        elif choice == "3":
            await example_3_batch_testing()
        elif choice == "4":
            await example_4_custom_configuration()
        elif choice == "5":
            await example_1_basic_usage()
            await example_2_login_scenario()
            await example_3_batch_testing()
        else:
            print("无效选择")
    
    except KeyboardInterrupt:
        print("\n用户中断执行")
    except Exception as e:
        print(f"\n执行出错: {e}")
        logging_utils.logger_print(
            msg="example execution failed",
            custom_logger=logger,
            use_exception=True,
            exception=e
        )
    
    print("\n示例执行完成")


if __name__ == "__main__":
    asyncio.run(main())
