"""ddddocr API客户端模块。

提供与ddddocr验证码识别服务的通信接口，支持多种验证码识别功能。
"""

import logging
import base64
from typing import Optional, Dict, Any, Tuple, List
from pathlib import Path
import io

import requests
from PIL import Image

from common.utils import logging_utils

logger = logging.getLogger(__name__)


class DDDDOCRClient:
    """ddddocr API客户端类。
    
    提供与ddddocr验证码识别服务的通信接口，支持滑块验证码、文字识别等功能。
    """
    
    def __init__(self, base_url: str = "http://************:7777", timeout: int = 30):
        """初始化ddddocr客户端。
        
        Args:
            base_url: ddddocr服务的基础URL
            timeout: 请求超时时间（秒）
        """
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        
        logging_utils.logger_print(
            msg=f"initialized ddddocr client with base_url: {self.base_url}",
            custom_logger=logger
        )
    
    def _encode_image(self, image_data: bytes) -> str:
        """将图片数据编码为base64字符串。
        
        Args:
            image_data: 图片二进制数据
            
        Returns:
            base64编码的图片字符串
        """
        return base64.b64encode(image_data).decode('utf-8')
    
    def _load_image_data(self, image) -> bytes:
        """加载图片数据。
        
        Args:
            image: 图片数据，可以是文件路径、PIL Image对象或字节数据
            
        Returns:
            图片的字节数据
        """
        if isinstance(image, (str, Path)):
            # 文件路径
            with open(image, 'rb') as f:
                return f.read()
        elif isinstance(image, Image.Image):
            # PIL Image对象
            buffer = io.BytesIO()
            image.save(buffer, format='PNG')
            return buffer.getvalue()
        elif isinstance(image, bytes):
            # 字节数据
            return image
        else:
            raise ValueError(f"unsupported image type: {type(image)}")
    
    def _make_request(self, endpoint: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """发送HTTP请求到ddddocr服务。
        
        Args:
            endpoint: API端点
            data: 请求数据
            
        Returns:
            响应数据
        """
        url = f"{self.base_url}{endpoint}"
        
        try:
            response = self.session.post(
                url,
                json=data,
                timeout=self.timeout,
                headers={'Content-Type': 'application/json'}
            )
            response.raise_for_status()
            
            result = response.json()
            logging_utils.logger_print(
                msg=f"ddddocr api request successful: {endpoint}",
                custom_logger=logger
            )
            return result
            
        except requests.exceptions.RequestException as e:
            logging_utils.logger_print(
                msg=f"ddddocr api request failed: {endpoint}",
                custom_logger=logger,
                use_exception=True,
                exception=e
            )
            raise
    
    def slide_comparison(self, sliding_image, back_image) -> int:
        """滑块比较识别。

        传入滑块图和背景图，返回滑块的目标位置。

        Args:
            sliding_image: 滑块图片
            back_image: 背景图片

        Returns:
            滑块目标位置的X坐标
        """
        sliding_data = self._load_image_data(sliding_image)
        back_data = self._load_image_data(back_image)

        data = {
            'slidingImage': self._encode_image(sliding_data),
            'backImage': self._encode_image(back_data)
        }

        result = self._make_request('/slideComparison', data)

        # 解析返回的位置信息
        if 'result' in result:
            return int(result['result'])

        # 如果返回格式不符合预期，记录日志并返回默认值
        logging_utils.logger_print(
            msg=f"unexpected slide comparison result format: {result}",
            custom_logger=logger
        )
        return 0
    
    def capcode(self, sliding_image, back_image, simple_target: bool = True) -> int:
        """验证码识别。

        传入滑块图和背景图，返回滑块的目标位置。

        Args:
            sliding_image: 滑块图片
            back_image: 背景图片
            simple_target: 是否使用简单目标模式

        Returns:
            滑块目标位置的X坐标
        """
        sliding_data = self._load_image_data(sliding_image)
        back_data = self._load_image_data(back_image)

        data = {
            'slidingImage': self._encode_image(sliding_data),
            'backImage': self._encode_image(back_data),
            'simpleTarget': simple_target
        }

        result = self._make_request('/capcode', data)

        # 解析返回的位置信息
        if 'result' in result:
            return int(result['result'])

        logging_utils.logger_print(
            msg=f"unexpected capcode result format: {result}",
            custom_logger=logger
        )
        return 0
    
    def classification(self, image) -> str:
        """图片分类识别。

        识别图片结果。

        Args:
            image: 要识别的图片

        Returns:
            识别结果字符串
        """
        image_data = self._load_image_data(image)

        data = {
            'image': self._encode_image(image_data)
        }

        result = self._make_request('/classification', data)

        # 返回识别结果
        if 'result' in result:
            return str(result['result'])

        logging_utils.logger_print(
            msg=f"unexpected classification result format: {result}",
            custom_logger=logger
        )
        return ""
    
    def detection(self, image) -> List[List[int]]:
        """目标检测。

        获取背景图中所有文字和图标的坐标位置。

        Args:
            image: 要检测的图片

        Returns:
            检测结果列表，每个元素是 [x1, y1, x2, y2] 格式的坐标
        """
        image_data = self._load_image_data(image)

        data = {
            'image': self._encode_image(image_data)
        }

        result = self._make_request('/detection', data)

        # 返回检测结果
        if 'result' in result:
            return result['result']
        elif isinstance(result, list):
            return result

        logging_utils.logger_print(
            msg=f"unexpected detection result format: {result}",
            custom_logger=logger
        )
        return []
    
    def calculate(self, image) -> str:
        """计算识别。

        识别并计算图片中的算术表达式。

        Args:
            image: 包含算术表达式的图片

        Returns:
            计算结果字符串
        """
        image_data = self._load_image_data(image)

        data = {
            'image': self._encode_image(image_data)
        }

        result = self._make_request('/calculate', data)

        # 返回计算结果
        if 'result' in result:
            return str(result['result'])

        logging_utils.logger_print(
            msg=f"unexpected calculate result format: {result}",
            custom_logger=logger
        )
        return ""
    
    def select(self, image) -> List[Dict[str, Any]]:
        """选择识别。

        返回图片点击结果，包含识别的文字和对应的坐标。

        Args:
            image: 要分析的图片

        Returns:
            识别结果列表，每个元素是 {识别文字: [x1, y1, x2, y2]} 格式
        """
        image_data = self._load_image_data(image)

        data = {
            'image': self._encode_image(image_data)
        }

        result = self._make_request('/select', data)

        # 返回选择结果
        if isinstance(result, list):
            return result

        logging_utils.logger_print(
            msg=f"unexpected select result format: {result}",
            custom_logger=logger
        )
        return []
    
    def close(self):
        """关闭客户端连接。"""
        if self.session:
            self.session.close()
            logging_utils.logger_print(
                msg="ddddocr client session closed",
                custom_logger=logger
            )
