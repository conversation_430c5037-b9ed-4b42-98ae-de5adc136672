"""验证码解决器工具类。

提供简化的接口来解决各种类型的验证码，包括滑块验证码、文字识别等。
"""

import logging
import asyncio
from typing import Optional, Dict, Any, List, Tuple
from pathlib import Path

from common.utils import logging_utils
from .ddddocr_client import DDDDOCRClient
from .slider_captcha_recognizer import SliderCaptchaRecognizer

logger = logging.getLogger(__name__)


class CaptchaSolver:
    """验证码解决器工具类。
    
    提供统一的接口来解决各种类型的验证码问题。
    """
    
    def __init__(self,
                 ddddocr_base_url: str = "http://10.168.1.201:7777",
                 headless: bool = True,
                 browser_type: str = "chromium"):
        """初始化验证码解决器。

        Args:
            ddddocr_base_url: ddddocr服务的基础URL
            headless: 是否使用无头模式运行浏览器
            browser_type: 浏览器类型 ("chromium", "firefox", "webkit")
        """
        self.ddddocr_base_url = ddddocr_base_url
        self.headless = headless
        self.browser_type = browser_type
        
        # 延迟初始化，只在需要时创建
        self._ddddocr_client: Optional[DDDDOCRClient] = None
        self._slider_recognizer: Optional[SliderCaptchaRecognizer] = None
        
        logging_utils.logger_print(
            msg="initialized captcha solver",
            custom_logger=logger
        )
    
    @property
    def ddddocr_client(self) -> DDDDOCRClient:
        """获取ddddocr客户端实例。"""
        if self._ddddocr_client is None:
            self._ddddocr_client = DDDDOCRClient(self.ddddocr_base_url)
        return self._ddddocr_client
    
    def get_slider_recognizer(self) -> SliderCaptchaRecognizer:
        """获取滑块识别器实例。"""
        if self._slider_recognizer is None:
            self._slider_recognizer = SliderCaptchaRecognizer(
                ddddocr_base_url=self.ddddocr_base_url,
                headless=self.headless,
                browser_type=self.browser_type
            )
        return self._slider_recognizer
    
    async def solve_slider_captcha_on_page(self,
                                          url: str,
                                          background_selector: str,
                                          slider_button_selector: str,
                                          slider_selector: Optional[str] = None,
                                          success_selector: Optional[str] = None,
                                          failure_selector: Optional[str] = None,
                                          use_slide_comparison: bool = True,
                                          max_retries: int = 3) -> bool:
        """在指定页面上解决滑块验证码。
        
        Args:
            url: 目标页面URL
            background_selector: 背景图元素选择器
            slider_button_selector: 滑块按钮选择器
            slider_selector: 滑块图元素选择器（可选）
            success_selector: 成功标识元素选择器
            failure_selector: 失败标识元素选择器
            use_slide_comparison: 是否使用slideComparison API
            max_retries: 最大重试次数
            
        Returns:
            是否成功解决验证码
        """
        recognizer = self.get_slider_recognizer()
        
        async with recognizer:
            # 导航到目标页面
            await recognizer.navigate_to_page(url)
            
            # 尝试解决验证码
            for attempt in range(max_retries):
                logging_utils.logger_print(
                    msg=f"attempting to solve slider captcha (attempt {attempt + 1}/{max_retries})",
                    custom_logger=logger
                )
                
                # 解决滑块验证码
                success = await recognizer.solve_slider_captcha(
                    background_selector=background_selector,
                    slider_button_selector=slider_button_selector,
                    slider_selector=slider_selector,
                    use_slide_comparison=use_slide_comparison
                )
                
                if success:
                    # 等待验证结果
                    result = await recognizer.wait_for_captcha_result(
                        success_selector=success_selector,
                        failure_selector=failure_selector
                    )
                    
                    if result is True:
                        logging_utils.logger_print(
                            msg="slider captcha solved successfully",
                            custom_logger=logger
                        )
                        return True
                    elif result is False:
                        logging_utils.logger_print(
                            msg=f"slider captcha verification failed, retrying... (attempt {attempt + 1})",
                            custom_logger=logger
                        )
                        # 等待一段时间后重试
                        await asyncio.sleep(2)
                        continue
                    else:
                        # 超时，可能需要刷新页面
                        logging_utils.logger_print(
                            msg="captcha verification timeout, refreshing page",
                            custom_logger=logger
                        )
                        await recognizer.navigate_to_page(url)
                        continue
                else:
                    logging_utils.logger_print(
                        msg=f"failed to solve slider captcha (attempt {attempt + 1})",
                        custom_logger=logger
                    )
                    await asyncio.sleep(1)
            
            logging_utils.logger_print(
                msg=f"failed to solve slider captcha after {max_retries} attempts",
                custom_logger=logger
            )
            return False
    
    def recognize_text_from_image(self, image_path: str) -> str:
        """从图片中识别文字。
        
        Args:
            image_path: 图片文件路径
            
        Returns:
            识别出的文字
        """
        logging_utils.logger_print(
            msg=f"recognizing text from image: {image_path}",
            custom_logger=logger
        )
        
        try:
            result = self.ddddocr_client.classification(image_path)
            logging_utils.logger_print(
                msg=f"text recognition result: {result}",
                custom_logger=logger
            )
            return result
        except Exception as e:
            logging_utils.logger_print(
                msg="error recognizing text from image",
                custom_logger=logger,
                use_exception=True,
                exception=e
            )
            return ""
    
    def detect_objects_in_image(self, image_path: str) -> List[List[int]]:
        """检测图片中的对象。

        Args:
            image_path: 图片文件路径

        Returns:
            检测结果列表，每个元素是 [x1, y1, x2, y2] 格式的坐标
        """
        logging_utils.logger_print(
            msg=f"detecting objects in image: {image_path}",
            custom_logger=logger
        )

        try:
            result = self.ddddocr_client.detection(image_path)
            logging_utils.logger_print(
                msg=f"object detection result: {len(result)} objects found",
                custom_logger=logger
            )
            return result
        except Exception as e:
            logging_utils.logger_print(
                msg="error detecting objects in image",
                custom_logger=logger,
                use_exception=True,
                exception=e
            )
            return []
    
    def calculate_math_from_image(self, image_path: str) -> str:
        """从图片中计算数学表达式。
        
        Args:
            image_path: 包含数学表达式的图片路径
            
        Returns:
            计算结果
        """
        logging_utils.logger_print(
            msg=f"calculating math from image: {image_path}",
            custom_logger=logger
        )
        
        try:
            result = self.ddddocr_client.calculate(image_path)
            logging_utils.logger_print(
                msg=f"math calculation result: {result}",
                custom_logger=logger
            )
            return result
        except Exception as e:
            logging_utils.logger_print(
                msg="error calculating math from image",
                custom_logger=logger,
                use_exception=True,
                exception=e
            )
            return ""
    
    def get_click_positions_from_image(self, image_path: str) -> List[Dict[str, Any]]:
        """从图片中获取点击位置。

        Args:
            image_path: 图片文件路径

        Returns:
            识别结果列表，每个元素是 {识别文字: [x1, y1, x2, y2]} 格式
        """
        logging_utils.logger_print(
            msg=f"getting click positions from image: {image_path}",
            custom_logger=logger
        )

        try:
            result = self.ddddocr_client.select(image_path)
            logging_utils.logger_print(
                msg=f"click positions result: {len(result)} items found",
                custom_logger=logger
            )
            return result
        except Exception as e:
            logging_utils.logger_print(
                msg="error getting click positions from image",
                custom_logger=logger,
                use_exception=True,
                exception=e
            )
            return []
    
    def close(self):
        """关闭所有资源。"""
        logging_utils.logger_print(
            msg="closing captcha solver resources",
            custom_logger=logger
        )
        
        if self._ddddocr_client:
            self._ddddocr_client.close()
            self._dddocr_client = None
        
        # 注意：slider_recognizer 需要在异步上下文中关闭
        if self._slider_recognizer:
            logging_utils.logger_print(
                msg="warning: slider recognizer should be closed in async context",
                custom_logger=logger
            )


# 便捷函数
async def solve_slider_captcha(url: str,
                              background_selector: str,
                              slider_button_selector: str,
                              slider_selector: Optional[str] = None,
                              success_selector: Optional[str] = None,
                              failure_selector: Optional[str] = None,
                              dddocr_base_url: str = "http://10.168.1.201:7777",
                              headless: bool = True,
                              max_retries: int = 3) -> bool:
    """便捷函数：解决滑块验证码。
    
    Args:
        url: 目标页面URL
        background_selector: 背景图元素选择器
        slider_button_selector: 滑块按钮选择器
        slider_selector: 滑块图元素选择器（可选）
        success_selector: 成功标识元素选择器
        failure_selector: 失败标识元素选择器
        dddocr_base_url: dddocr服务的基础URL
        headless: 是否使用无头模式
        max_retries: 最大重试次数
        
    Returns:
        是否成功解决验证码
    """
    solver = CaptchaSolver(ddddocr_base_url=ddddocr_base_url, headless=headless)
    
    try:
        return await solver.solve_slider_captcha_on_page(
            url=url,
            background_selector=background_selector,
            slider_button_selector=slider_button_selector,
            slider_selector=slider_selector,
            success_selector=success_selector,
            failure_selector=failure_selector,
            max_retries=max_retries
        )
    finally:
        solver.close()


def recognize_text(image_path: str, ddddocr_base_url: str = "http://10.168.1.201:7777") -> str:
    """便捷函数：识别图片中的文字。
    
    Args:
        image_path: 图片文件路径
        ddddocr_base_url: ddddocr服务的基础URL
        
    Returns:
        识别出的文字
    """
    solver = CaptchaSolver(ddddocr_base_url=ddddocr_base_url)
    
    try:
        return solver.recognize_text_from_image(image_path)
    finally:
        solver.close()
