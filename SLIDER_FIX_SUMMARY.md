# 腾讯滑块验证码拖拽距离修复总结

## 问题描述

在 `TencentSliderSolver` 中的 `solve_captcha` 函数通过 dddocr 得到的值是滑块目标图像位置的 x 坐标，是相对于整个背景图像来说的 x 坐标【px】。但是 `_drag_slider` 实际要滑动的长度存在偏差，经过测试发现**拖拽的长度始终缺少半个滑块的宽度**。

## 问题根因

**原始错误逻辑：**
```python
# 第930行（修复前）
main_end_x = main_start_x + target_x
```

这里错误地将 dddocr 返回的绝对坐标 `target_x` 当作相对偏移量使用。

**dddocr 返回值的实际含义：**
- `target_x` 是滑块目标图像在整个背景图像中的**绝对 x 坐标**
- 不是滑块需要移动的**相对距离**
- **关键发现**：dddocr 返回的坐标很可能是基于滑块**左边缘**的位置，而不是中心点

## 修复方案

### 1. 获取滑块当前位置
```python
# 获取滑块按钮和背景图的位置信息
button_box = await slider_button.bounding_box()
bg_box = await bg_element.bounding_box()

# 计算滑块当前在背景图中的相对位置（使用左边缘作为参考点）
slider_left_edge_x = button_box['x']
slider_current_x_in_bg = slider_left_edge_x - bg_box['x']
```

### 2. 计算正确的移动距离
```python
# 计算需要移动的距离
# target_x 是目标位置在背景图中的绝对坐标
# slider_current_x_in_bg 是滑块当前在背景图中的位置
move_distance = target_x - slider_current_x_in_bg
```

### 3. 使用正确的移动距离进行拖拽
```python
# 修复前
current_x = main_start_x + target_x * progress

# 修复后
current_x = main_start_x + move_distance * progress
```

## 修复的关键变更

### 文件：`src/captcha/recognizer/t_sec/tencent_slider_solver.py`

1. **函数文档更新**（第852-856行）：
   ```python
   async def _drag_slider(self, target_x: int) -> bool:
       """拖拽滑块到目标位置。
       
       Args:
           target_x: dddocr返回的目标位置绝对x坐标（相对于背景图）
       """
   ```

2. **添加背景图元素获取**（第876-880行）：
   ```python
   # 查找背景图元素
   bg_element = await self.page.query_selector('#slideBg')
   if not bg_element:
       logging_utils.logger_print(msg="background element not found", custom_logger=logger)
       return False
   ```

3. **修复参考点不一致问题**（第893-910行）：
   ```python
   # 使用滑块左边缘作为参考点（与dddocr返回值保持一致）
   slider_left_edge_x = button_box['x']
   slider_current_x_in_bg = slider_left_edge_x - bg_box['x']

   # 同时记录中心点位置用于对比
   slider_center_x = button_box['x'] + button_box['width'] / 2
   slider_center_x_in_bg = slider_center_x - bg_box['x']
   ```

4. **计算正确的移动距离**（第912-920行）：
   ```python
   # 使用一致的参考点计算移动距离
   # target_x 是目标左边缘位置，slider_current_x_in_bg 是当前左边缘位置
   move_distance = target_x - slider_current_x_in_bg
   ```

5. **使用正确距离进行拖拽**（第983行）：
   ```python
   current_x = main_start_x + move_distance * progress
   ```

6. **更新步数计算**（第969行）：
   ```python
   steps = max(10, int(abs(move_distance) // 5))  # 修复浮点数类型错误
   ```

## 修复效果

- ✅ **正确计算移动距离**：使用目标绝对位置减去当前绝对位置
- ✅ **精确拖拽控制**：滑块移动到正确的目标位置
- ✅ **详细日志记录**：便于调试和监控拖拽过程
- ✅ **保持原有逻辑**：iframe 处理和坐标转换逻辑不变
- ✅ **修复类型错误**：解决浮点数导致的 `'float' object cannot be interpreted as an integer` 错误

## 测试建议

1. **单元测试**：验证距离计算逻辑
2. **集成测试**：使用真实的腾讯滑块验证码进行测试
3. **日志监控**：观察修复后的移动距离计算是否正确

## 相关文件

- `src/captcha/recognizer/t_sec/tencent_slider_solver.py` - 主要修复文件
- `test_slider_fix.py` - 测试脚本
- `SLIDER_FIX_SUMMARY.md` - 本文档

---

**修复完成时间**：2025-09-17  
**修复状态**：✅ 已完成
