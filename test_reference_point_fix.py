#!/usr/bin/env python3
"""测试滑块参考点修复的脚本。

用于验证dddocr返回坐标的参考点问题修复。
"""

def test_reference_point_calculation():
    """测试不同参考点的计算结果。"""
    print("=== 滑块参考点计算测试 ===")
    
    # 模拟数据
    print("\n模拟场景：")
    print("- 滑块按钮宽度: 40px")
    print("- 滑块按钮当前位置: x=20px (相对于背景图)")
    print("- dddocr返回的target_x: 100px")
    
    button_width = 40
    button_current_x = 20  # 滑块左边缘位置
    target_x = 100
    
    # 方案1：使用左边缘作为参考点（当前修复方案）
    slider_left_edge = button_current_x
    move_distance_left = target_x - slider_left_edge
    
    print(f"\n方案1 - 左边缘参考点：")
    print(f"- 滑块当前左边缘: {slider_left_edge}px")
    print(f"- 移动距离: {target_x} - {slider_left_edge} = {move_distance_left}px")
    print(f"- 移动后左边缘位置: {slider_left_edge + move_distance_left}px")
    
    # 方案2：使用中心点作为参考点（原始方案）
    slider_center = button_current_x + button_width / 2
    move_distance_center = target_x - slider_center
    
    print(f"\n方案2 - 中心点参考点：")
    print(f"- 滑块当前中心: {slider_center}px")
    print(f"- 移动距离: {target_x} - {slider_center} = {move_distance_center}px")
    print(f"- 移动后中心位置: {slider_center + move_distance_center}px")
    print(f"- 移动后左边缘位置: {slider_center + move_distance_center - button_width/2}px")
    
    # 方案3：假设dddocr返回的是中心点坐标
    print(f"\n方案3 - 假设target_x是目标中心点：")
    target_center = target_x
    target_left_edge = target_center - button_width / 2
    move_distance_to_center = target_center - slider_center
    
    print(f"- 目标中心点: {target_center}px")
    print(f"- 目标左边缘: {target_left_edge}px")
    print(f"- 移动距离: {target_center} - {slider_center} = {move_distance_to_center}px")
    
    print(f"\n差异分析：")
    print(f"- 方案1与方案2的差异: {move_distance_left - move_distance_center}px = {button_width/2}px (半个滑块宽度)")
    print(f"- 如果实际缺少半个滑块宽度，说明dddocr返回的可能是中心点坐标")


def test_adaptive_solution():
    """测试自适应解决方案。"""
    print("\n=== 自适应解决方案 ===")
    print("可以在代码中添加一个参数来选择参考点：")
    
    code_example = '''
# 在_drag_slider方法中添加参考点选择
def calculate_move_distance(target_x, button_box, bg_box, reference_point='left_edge'):
    """
    计算移动距离，支持不同的参考点
    
    Args:
        target_x: dddocr返回的目标坐标
        button_box: 滑块按钮边界框
        bg_box: 背景图边界框
        reference_point: 参考点类型 ('left_edge', 'center', 'auto')
    """
    if reference_point == 'left_edge':
        # 使用左边缘作为参考点
        slider_current_x = button_box['x'] - bg_box['x']
        move_distance = target_x - slider_current_x
    elif reference_point == 'center':
        # 使用中心点作为参考点
        slider_center_x = button_box['x'] + button_box['width'] / 2
        slider_current_x = slider_center_x - bg_box['x']
        move_distance = target_x - slider_current_x
    elif reference_point == 'auto':
        # 自动检测：先尝试左边缘，如果失败则尝试中心点
        # 这需要在实际测试中根据成功率来判断
        pass
    
    return move_distance
'''
    
    print(code_example)


if __name__ == "__main__":
    test_reference_point_calculation()
    test_adaptive_solution()
    
    print("\n=== 建议 ===")
    print("1. 先测试当前的左边缘参考点修复")
    print("2. 如果仍然有偏差，可以尝试以下方案：")
    print("   - 在TencentSliderSolver中添加reference_point参数")
    print("   - 支持'left_edge'、'center'、'auto'三种模式")
    print("   - 根据实际测试结果选择最佳参考点")
    print("3. 观察日志中的详细坐标信息来判断dddocr的返回值含义")
