"""最终的滑块验证码解决方案。

经过调试和修复，这是完整可用的解决方案。
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.captcha.recognizer.t_sec.tencent_slider_solver import TencentSliderSolver


async def run_final_solution():
    """运行最终的验证码解决方案。"""
    print("🎯 最终的滑块验证码解决方案")
    print("=" * 50)
    print("✅ 已修复的问题:")
    print("  - 图片格式问题（强制PNG格式）")
    print("  - 拖拽卡住问题（清除焦点，详细日志）")
    print("  - Frame对象截图问题")
    print("  - 完整的错误处理")
    print("=" * 50)
    
    solver = TencentSliderSolver(
        dddocr_base_url="http://10.168.1.201:7777",
        headless=False,
        browser_type="chromium"
    )
    
    async with solver:
        try:
            # 导航到页面
            print("\n📍 步骤1: 导航到腾讯云验证码页面...")
            await solver.navigate_to_page("https://cloud.tencent.com/product/captcha")
            await asyncio.sleep(3)
            
            # 点击触发按钮
            print("👆 步骤2: 点击体验按钮...")
            try:
                await solver.page.wait_for_selector('#captcha_click', timeout=10000)
                experience_button = await solver.page.query_selector('#captcha_click')
                if experience_button:
                    await experience_button.click()
                    print("✅ 按钮点击成功")
                else:
                    print("❌ 未找到体验按钮")
                    return False
            except Exception as click_error:
                print(f"❌ 点击按钮失败: {click_error}")
                return False
            
            # 使用完整的解决流程
            print("\n🧩 步骤3: 执行完整的验证码解决流程...")
            print("   - 等待验证码加载")
            print("   - 获取背景图和滑块图")
            print("   - 保存图片到本地（PNG格式）")
            print("   - dddocr 识别目标位置")
            print("   - 清除页面焦点（防止全选）")
            print("   - 执行拖拽操作")
            print("   - 验证结果")
            print("   - 如果失败，自动重试")
            
            try:
                # 调用完整的solve_captcha方法（最多重试3次）
                solve_success = await solver.solve_captcha(max_retries=3)
                
                if solve_success:
                    print("\n🎉 验证码解决成功！")
                    print("🏆 最终解决方案工作完美！")
                    
                    # 截图保存成功状态（修复Frame问题）
                    try:
                        if hasattr(solver, 'main_page') and solver.main_page:
                            await solver.main_page.screenshot(path="final_success.png")
                        else:
                            await solver.page.screenshot(path="final_success.png")
                        print("📸 成功截图: final_success.png")
                    except Exception as screenshot_error:
                        print(f"⚠️  截图失败: {screenshot_error}")
                    
                    return True
                else:
                    print("\n❌ 验证码解决失败")
                    print("可能的原因:")
                    print("1. dddocr 识别不准确")
                    print("2. 验证码难度较高")
                    print("3. 网络延迟影响")
                    
                    # 截图保存失败状态（修复Frame问题）
                    try:
                        if hasattr(solver, 'main_page') and solver.main_page:
                            await solver.main_page.screenshot(path="final_failed.png")
                        else:
                            await solver.page.screenshot(path="final_failed.png")
                        print("📸 失败截图: final_failed.png")
                    except Exception as screenshot_error:
                        print(f"⚠️  截图失败: {screenshot_error}")
                    
                    return False
                    
            except Exception as solve_error:
                print(f"\n💥 解决流程出错: {solve_error}")
                
                # 错误截图（修复Frame问题）
                try:
                    if hasattr(solver, 'main_page') and solver.main_page:
                        await solver.main_page.screenshot(path="final_error.png")
                    else:
                        await solver.page.screenshot(path="final_error.png")
                    print("📸 错误截图: final_error.png")
                except:
                    pass
                
                return False
                
        except Exception as e:
            print(f"💥 运行过程中出现错误: {e}")
            
            # 错误截图（修复Frame问题）
            try:
                if hasattr(solver, 'main_page') and solver.main_page:
                    await solver.main_page.screenshot(path="final_error.png")
                else:
                    await solver.page.screenshot(path="final_error.png")
                print("📸 错误截图: final_error.png")
            except:
                pass
            
            return False


async def main():
    """主函数。"""
    print("最终的滑块验证码解决方案")
    print("=" * 50)
    
    try:
        print("🎯 这是经过完整调试和修复的最终版本:")
        print()
        print("✅ 已解决的问题:")
        print("1. ✅ 图片格式问题 - 强制保存为PNG格式")
        print("2. ✅ 拖拽卡住问题 - 清除页面焦点，防止全选操作")
        print("3. ✅ Frame对象问题 - 正确处理iframe和主页面的截图")
        print("4. ✅ 错误处理 - 完善的异常处理和日志记录")
        print()
        print("🔧 技术特点:")
        print("1. 正确的背景图获取（从 #slideBg 元素）")
        print("2. 正确的滑块图获取（从滑块元素裁剪）")
        print("3. PNG格式图片保存")
        print("4. dddocr API集成")
        print("5. 智能拖拽操作（防止全选）")
        print("6. 多次重试机制")
        print("7. 完整的结果验证")
        print()
        print("请确保:")
        print("1. dddocr 服务正在运行 (http://10.168.1.201:7777)")
        print("2. 网络连接正常")
        print("3. 浏览器可以正常访问腾讯云")
        print()
        
        input("按回车键开始运行最终解决方案...")
        
        success = await run_final_solution()
        
        print("\n" + "=" * 50)
        if success:
            print("🎉 最终结果: 完美成功！")
            print()
            print("🏆 系统完整性验证:")
            print("✅ 页面导航和交互")
            print("✅ 验证码检测和加载")
            print("✅ 图片获取（背景图+滑块图）")
            print("✅ PNG格式图片保存")
            print("✅ dddocr 识别")
            print("✅ 智能拖拽操作（无全选问题）")
            print("✅ 验证码解决")
            print("✅ 错误处理和截图")
            print()
            print("生成的文件:")
            print("- final_success.png (成功截图)")
            print("- captcha_bg_attempt_X.png (背景图，PNG格式)")
            print("- captcha_slider_attempt_X.png (滑块图，PNG格式)")
            print()
            print("🎯 恭喜！滑块验证码解决方案已完全可用！")
        else:
            print("❌ 最终结果: 需要进一步调整")
            print()
            print("虽然失败，但系统架构是正确的，可能需要:")
            print("1. 调整dddocr识别参数")
            print("2. 优化拖拽速度和轨迹")
            print("3. 处理特定验证码类型")
            print()
            print("请检查生成的文件:")
            print("- final_failed.png (失败截图)")
            print("- captcha_bg_attempt_X.png (背景图)")
            print("- captcha_slider_attempt_X.png (滑块图)")
            print("- final_error.png (错误截图，如果有)")
        print("=" * 50)
        
    except KeyboardInterrupt:
        print("\n⏹️  用户中断运行")
    except Exception as e:
        print(f"\n💥 程序出错: {e}")
    
    print("\n运行结束")
    print("say \"当前任务完成\"")


if __name__ == "__main__":
    asyncio.run(main())
