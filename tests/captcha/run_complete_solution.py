"""运行完整的滑块验证码解决方案。

使用改进后的图片获取逻辑和完整的解决流程。
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.common.utils import logging_utils
from src.captcha.recognizer.t_sec.tencent_slider_solver import TencentSliderSolver


async def run_complete_solution():
    """运行完整的验证码解决方案。"""
    print("🎯 运行完整的滑块验证码解决方案")
    print("=" * 50)
    print("功能特点:")
    print("✅ 正确的背景图获取（从 #slideBg 元素）")
    print("✅ 正确的滑块图获取（从滑块元素裁剪）")
    print("✅ 完整的 dddocr 识别流程")
    print("✅ 自动拖拽和验证")
    print("✅ 多次重试机制")
    print("=" * 50)
    
    solver = TencentSliderSolver(
        dddocr_base_url="http://10.168.1.201:7777",
        headless=False,
        browser_type="chromium"
    )
    
    async with solver:
        try:
            # 导航到页面
            print("\n📍 步骤1: 导航到腾讯云验证码页面...")
            await solver.navigate_to_page("https://cloud.tencent.com/product/captcha")
            await asyncio.sleep(3)
            
            # 点击触发按钮
            print("👆 步骤2: 点击体验按钮...")
            try:
                await solver.page.wait_for_selector('#captcha_click', timeout=10000)
                experience_button = await solver.page.query_selector('#captcha_click')
                if experience_button:
                    await experience_button.click()
                    print("✅ 按钮点击成功")
                else:
                    print("❌ 未找到体验按钮")
                    return False
            except Exception as click_error:
                print(f"❌ 点击按钮失败: {click_error}")
                return False
            
            # 使用完整的解决流程
            print("\n🧩 步骤3: 执行完整的验证码解决流程...")
            print("   - 等待验证码加载")
            print("   - 获取背景图和滑块图")
            print("   - 保存图片到本地")
            print("   - dddocr 识别目标位置")
            print("   - 执行拖拽操作")
            print("   - 验证结果")
            print("   - 如果失败，自动重试")
            
            try:
                # 调用完整的solve_captcha方法（最多重试3次）
                solve_success = await solver.solve_captcha(max_retries=3)
                
                if solve_success:
                    print("\n🎉 验证码解决成功！")
                    print("🏆 完整的解决方案工作正常！")
                    
                    # 截图保存成功状态
                    try:
                        if hasattr(solver, 'main_page') and solver.main_page:
                            await solver.main_page.screenshot(path="solution_success.png")
                        else:
                            await solver.page.screenshot(path="solution_success.png")
                        print("📸 成功截图: solution_success.png")
                    except Exception as screenshot_error:
                        print(f"⚠️  截图失败: {screenshot_error}")
                    
                    return True
                else:
                    print("\n❌ 验证码解决失败")
                    print("可能的原因:")
                    print("1. dddocr 识别不准确")
                    print("2. 拖拽操作有问题")
                    print("3. 网络延迟影响")
                    print("4. 验证码难度较高")
                    
                    # 截图保存失败状态
                    try:
                        if hasattr(solver, 'main_page') and solver.main_page:
                            await solver.main_page.screenshot(path="solution_failed.png")
                        else:
                            await solver.page.screenshot(path="solution_failed.png")
                        print("📸 失败截图: solution_failed.png")
                    except Exception as screenshot_error:
                        print(f"⚠️  截图失败: {screenshot_error}")
                    
                    # 手动获取图片进行分析
                    print("\n🔍 手动获取图片进行分析...")
                    try:
                        bg_data, slider_data = await solver.get_captcha_images()
                        
                        if bg_data and slider_data:
                            print(f"✅ 图片获取成功: bg={len(bg_data)} bytes, slider={len(slider_data)} bytes")
                            
                            # 保存图片用于检查
                            with open("analysis_bg.png", "wb") as f:
                                f.write(bg_data)
                            with open("analysis_slider.png", "wb") as f:
                                f.write(slider_data)
                            print("💾 图片已保存: analysis_bg.png, analysis_slider.png")
                            
                            # dddocr识别
                            from src.captcha.recognizer.t_sec.dddocr_client import DDDOCRClient
                            client = DDDOCRClient("http://10.168.1.201:7777")
                            x = client.capcode(slider_data, bg_data)
                            
                            print(f"🧩 dddocr 识别结果: x={x}")
                            
                            if x > 0 and x < 400:
                                print("✅ 识别结果在合理范围内")
                                print("   问题可能在拖拽或验证环节")
                            else:
                                print("⚠️  识别结果可能不准确")
                                
                        else:
                            print("❌ 图片获取失败")
                            
                    except Exception as manual_error:
                        print(f"❌ 手动分析失败: {manual_error}")
                    
                    return False
                    
            except Exception as solve_error:
                print(f"\n💥 解决流程出错: {solve_error}")
                
                # 错误截图
                try:
                    await solver.page.screenshot(path="solution_error.png")
                    print("📸 错误截图: solution_error.png")
                except:
                    pass
                
                return False
                
        except Exception as e:
            print(f"💥 运行过程中出现错误: {e}")
            
            # 错误截图
            try:
                await solver.page.screenshot(path="solution_error.png")
                print("📸 错误截图: solution_error.png")
            except:
                pass
            
            return False


async def main():
    """主函数。"""
    print("完整的滑块验证码解决方案")
    print("=" * 50)
    
    try:
        print("这个程序将运行完整的验证码解决流程:")
        print("1. 页面导航和按钮点击")
        print("2. 验证码检测和图片获取")
        print("3. 图片保存到本地")
        print("4. dddocr 识别和拖拽操作")
        print("5. 结果验证和重试机制")
        print()
        print("请确保:")
        print("1. dddocr 服务正在运行 (http://10.168.1.201:7777)")
        print("2. 网络连接正常")
        print("3. 浏览器可以正常访问腾讯云")
        print()
        
        input("按回车键开始运行...")
        
        success = await run_complete_solution()
        
        print("\n" + "=" * 50)
        if success:
            print("🎉 运行结果: 成功！")
            print("完整的解决方案工作正常：")
            print("✅ 页面导航和交互")
            print("✅ 验证码检测和加载")
            print("✅ 图片获取（背景图+滑块图）")
            print("✅ dddocr 识别")
            print("✅ 拖拽操作")
            print("✅ 验证码解决")
            print()
            print("生成的文件:")
            print("- solution_success.png (成功截图)")
        else:
            print("❌ 运行结果: 失败")
            print("但这不一定意味着代码有问题，可能是:")
            print("1. 验证码难度较高")
            print("2. 网络延迟影响")
            print("3. dddocr 识别准确率问题")
            print("4. 拖拽参数需要调整")
            print()
            print("请检查生成的文件:")
            print("- solution_failed.png (失败截图)")
            print("- analysis_bg.png (背景图)")
            print("- analysis_slider.png (滑块图)")
            print("- solution_error.png (错误截图，如果有)")
        print("=" * 50)
        
    except KeyboardInterrupt:
        print("\n⏹️  用户中断运行")
    except Exception as e:
        print(f"\n💥 程序出错: {e}")
    
    print("\n运行结束")


if __name__ == "__main__":
    asyncio.run(main())
